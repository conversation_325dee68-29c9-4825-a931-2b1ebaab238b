import emailjs from '@emailjs/browser';

// EmailJS Configuration
const EMAILJS_CONFIG = {
  publicKey: 'x_6ueEx3CRUly-ZVu',
  serviceId: 'service_ff9iykb',
  templateIds: {
    free: 'template_welcome', // Generic template name
    pro: 'template_welcome',  // Using same template for all plans for now
    enterprise: 'template_welcome', // Using same template for all plans for now
  },
};

// Initialize EmailJS
let isInitialized = false;

export const initializeEmailJS = () => {
  if (!isInitialized && typeof window !== 'undefined') {
    try {
      emailjs.init({
        publicKey: EMAILJS_CONFIG.publicKey,
      });
      isInitialized = true;
      console.log('✅ EmailJS initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ EmailJS initialization failed:', error);
      return false;
    }
  }
  return isInitialized;
};

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'free' | 'pro' | 'enterprise';
}

export const emailJSService = {
  sendWelcomeEmail: async ({ to, name, planType }: EmailRequest): Promise<boolean> => {
    try {
      console.log(`📧 Starting email send process for ${planType} plan to ${to}...`);

      // Ensure EmailJS is initialized
      const initialized = initializeEmailJS();
      
      if (!initialized) {
        console.error('❌ EmailJS not initialized');
        return false;
      }

      // Wait a bit to ensure EmailJS is fully ready
      await new Promise(resolve => setTimeout(resolve, 500));

      const templateId = EMAILJS_CONFIG.templateIds[planType];
      
      if (!templateId) {
        console.error(`❌ No template found for plan type: ${planType}`);
        return false;
      }

      // Validate email address
      if (!to || !to.includes('@')) {
        console.error('❌ Invalid email address:', to);
        return false;
      }

      // Template parameters that will be sent to EmailJS
      const templateParams = {
        to_email: to, // This is the key field that EmailJS uses
        to_name: name,
        user_name: name,
        plan_type: planType,
        plan_name: planType.charAt(0).toUpperCase() + planType.slice(1),
        from_name: 'AutoML Pro Team',
        reply_to: '<EMAIL>',
        message: `Welcome to AutoML Pro! You've successfully signed up for the ${planType} plan.`,
        site_url: typeof window !== 'undefined' ? window.location.origin : 'https://automlpro.netlify.app',
        dashboard_url: typeof window !== 'undefined' ? `${window.location.origin}/dashboard` : 'https://automlpro.netlify.app/dashboard',
        pricing_url: typeof window !== 'undefined' ? `${window.location.origin}/pricing` : 'https://automlpro.netlify.app/pricing',
        chat_url: typeof window !== 'undefined' ? `${window.location.origin}/chat` : 'https://automlpro.netlify.app/chat',
      };

      console.log(`📧 Sending ${planType} welcome email...`);
      console.log('Template params:', {
        to_email: templateParams.to_email,
        to_name: templateParams.to_name,
        plan_type: templateParams.plan_type,
        template_id: templateId
      });

      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        templateId,
        templateParams
      );

      console.log('EmailJS result:', result);

      if (result.status === 200) {
        console.log(`✅ ${planType} welcome email sent successfully to ${to}`);
        return true;
      } else {
        console.error('❌ EmailJS failed with status:', result.status, result.text);
        return false;
      }

    } catch (error) {
      console.error('❌ Error sending welcome email via EmailJS:', error);
      return false;
    }
  }
};

// Test function to verify EmailJS is working
export const testEmailJS = async (testEmail: string = '<EMAIL>') => {
  try {
    console.log('🧪 Testing EmailJS configuration...');

    const initialized = initializeEmailJS();
    if (!initialized) {
      console.error('❌ EmailJS not initialized');
      return false;
    }

    // Use a simple template for testing
    const testParams = {
      to_email: testEmail,
      to_name: 'Test User',
      user_name: 'Test User',
      plan_type: 'free',
      plan_name: 'Free',
      from_name: 'AutoML Pro Team',
      reply_to: '<EMAIL>',
      message: 'This is a test email from AutoML Pro',
      site_url: 'https://automlpro.netlify.app',
      dashboard_url: 'https://automlpro.netlify.app/dashboard',
      pricing_url: 'https://automlpro.netlify.app/pricing',
      chat_url: 'https://automlpro.netlify.app/chat',
    };

    console.log('🧪 Sending test email with params:', testParams);

    const result = await emailjs.send(
      EMAILJS_CONFIG.serviceId,
      'template_welcome', // Using generic template for test
      testParams
    );

    console.log('🧪 Test email result:', result);
    return result.status === 200;
  } catch (error) {
    console.error('🧪 Test email failed:', error);
    return false;
  }
};

// Export configuration for debugging
export const getEmailJSConfig = () => EMAILJS_CONFIG;

// Export initialization status
export const getInitializationStatus = () => isInitialized;