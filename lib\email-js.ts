import emailjs from '@emailjs/browser';

// EmailJS Configuration
const EMAILJS_CONFIG = {
  publicKey: 'x_6ueEx3CRUly-ZVu',
  serviceId: 'service_ff9iykb',
  templateIds: {
    free: 'template_free_welcome',
    pro: 'template_pro_welcome', // You'll need to create this template in EmailJS
    enterprise: 'template_free_welcome', // Using free template for now
  },
};

// Initialize EmailJS
let isInitialized = false;

export const initializeEmailJS = () => {
  if (!isInitialized && typeof window !== 'undefined') {
    try {
      emailjs.init({
        publicKey: EMAILJS_CONFIG.publicKey,
      });
      isInitialized = true;
      console.log('✅ EmailJS initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ EmailJS initialization failed:', error);
      return false;
    }
  }
  return isInitialized;
};

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'free' | 'pro' | 'enterprise';
}

export const emailJSService = {
  sendWelcomeEmail: async ({ to, name, planType }: EmailRequest): Promise<boolean> => {
    try {
      console.log(`📧 Starting email send process for ${planType} plan to ${to}...`);

      // Ensure EmailJS is initialized
      const initialized = initializeEmailJS();
      
      if (!initialized) {
        console.error('❌ EmailJS not initialized');
        return false;
      }

      // Wait a bit to ensure EmailJS is fully ready
      await new Promise(resolve => setTimeout(resolve, 500));

      const templateId = EMAILJS_CONFIG.templateIds[planType];
      
      if (!templateId) {
        console.error(`❌ No template found for plan type: ${planType}`);
        return false;
      }

      // Validate email address
      if (!to || !to.includes('@')) {
        console.error('❌ Invalid email address:', to);
        return false;
      }

      // Template parameters that match your EmailJS templates exactly
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://684d5cf55754fab77a68c3d4--automlgpt.netlify.app';

      const templateParams = {
        // Core email fields (matching your EmailJS template setup)
        to_email: to,
        to_name: name,

        // Plan information
        plan_type: planType,
        plan_name: planType.charAt(0).toUpperCase() + planType.slice(1),
        plan_price: planType === 'pro' ? '$15/month' : planType === 'enterprise' ? 'Custom Pricing' : 'Free',

        // URLs for your template
        site_url: baseUrl,
        cta_url: `${baseUrl}/dashboard`,
        cta_text: planType === 'free' ? 'Start Building Your First Model' : 'Access Your Dashboard',

        // Contact information
        reply_to: '<EMAIL>',

        // Additional fields that might be useful
        from_name: 'AutoML Pro Team',
        user_name: name,
        message: `Welcome to AutoML Pro! You've successfully signed up for the ${planType} plan.`,
        dashboard_url: `${baseUrl}/dashboard`,
        pricing_url: `${baseUrl}/pricing`,
      };

      console.log(`📧 Sending ${planType} welcome email...`);
      console.log('Template params:', {
        to_email: templateParams.to_email,
        to_name: templateParams.to_name,
        plan_type: templateParams.plan_type,
        template_id: templateId
      });

      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        templateId,
        templateParams
      );

      console.log('EmailJS result:', result);

      if (result.status === 200) {
        console.log(`✅ ${planType} welcome email sent successfully to ${to}`);
        return true;
      } else {
        console.error('❌ EmailJS failed with status:', result.status, result.text);
        return false;
      }

    } catch (error) {
      console.error('❌ Error sending welcome email via EmailJS:', error);
      return false;
    }
  }
};

// Test function to verify EmailJS is working
export const testEmailJS = async (testEmail: string = '<EMAIL>') => {
  try {
    console.log('🧪 Testing EmailJS configuration...');

    const initialized = initializeEmailJS();
    if (!initialized) {
      console.error('❌ EmailJS not initialized');
      return false;
    }

    // Test parameters matching your template exactly
    const testParams = {
      to_email: testEmail,
      to_name: 'Test User',
      plan_type: 'free',
      plan_name: 'Free',
      plan_price: 'Free',
      site_url: 'https://684d5cf55754fab77a68c3d4--automlgpt.netlify.app',
      cta_url: 'https://684d5cf55754fab77a68c3d4--automlgpt.netlify.app/dashboard',
      cta_text: 'Start Building Your First Model',
      reply_to: '<EMAIL>',
      from_name: 'AutoML Pro Team',
      user_name: 'Test User',
      message: 'This is a test email from AutoML Pro',
      dashboard_url: 'https://684d5cf55754fab77a68c3d4--automlgpt.netlify.app/dashboard',
      pricing_url: 'https://684d5cf55754fab77a68c3d4--automlgpt.netlify.app/pricing',
    };

    console.log('🧪 Sending test email with params:', testParams);

    const result = await emailjs.send(
      EMAILJS_CONFIG.serviceId,
      'template_free_welcome', // Using your actual free welcome template
      testParams
    );

    console.log('🧪 Test email result:', result);
    return result.status === 200;
  } catch (error) {
    console.error('🧪 Test email failed:', error);
    return false;
  }
};

// Export configuration for debugging
export const getEmailJSConfig = () => EMAILJS_CONFIG;

// Export initialization status
export const getInitializationStatus = () => isInitialized;