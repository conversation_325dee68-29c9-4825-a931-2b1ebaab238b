import emailjs from '@emailjs/browser';

// EmailJS Configuration
const EMAILJS_CONFIG = {
  publicKey: 'x_6ueEx3CRUly-ZVu',
  serviceId: 'service_ff9iykb',
  templateIds: {
    free: 'template_free_welcome',
    pro: 'template_pro_welcome',
    enterprise: 'template_pro_welcome', // Using pro template for enterprise until you create enterprise template
  },
};

// Initialize EmailJS
let isInitialized = false;

export const initializeEmailJS = () => {
  if (!isInitialized && typeof window !== 'undefined') {
    try {
      emailjs.init(EMAILJS_CONFIG.publicKey);
      isInitialized = true;
      console.log('✅ EmailJS initialized successfully');
      console.log('📧 Service ID:', EMAILJS_CONFIG.serviceId);
      console.log('🔑 Public Key:', EMAILJS_CONFIG.publicKey);
    } catch (error) {
      console.error('❌ EmailJS initialization failed:', error);
    }
  }
};

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'free' | 'pro' | 'enterprise';
}

export const emailJSService = {
  sendWelcomeEmail: async ({ to, name, planType }: EmailRequest): Promise<boolean> => {
    try {
      // Ensure EmailJS is initialized
      initializeEmailJS();

      if (!isInitialized) {
        console.error('❌ EmailJS not initialized');
        return false;
      }

      const templateId = EMAILJS_CONFIG.templateIds[planType];
      
      if (!templateId) {
        console.error(`❌ No template found for plan type: ${planType}`);
        return false;
      }

      // Template parameters that will be sent to EmailJS
      const templateParams = {
        to_email: to,
        to_name: name,
        user_name: name,
        plan_type: planType,
        plan_name: planType.charAt(0).toUpperCase() + planType.slice(1),
        from_name: 'AutoML Pro Team',
        reply_to: '<EMAIL>',
        site_url: typeof window !== 'undefined' ? window.location.origin : 'https://automlpro.netlify.app',
        dashboard_url: typeof window !== 'undefined' ? `${window.location.origin}/dashboard` : 'https://automlpro.netlify.app/dashboard',
        pricing_url: typeof window !== 'undefined' ? `${window.location.origin}/pricing` : 'https://automlpro.netlify.app/pricing',
        chat_url: typeof window !== 'undefined' ? `${window.location.origin}/chat` : 'https://automlpro.netlify.app/chat',
      };

      console.log(`📧 Sending ${planType} welcome email to ${to}...`);
      console.log('📋 Template ID:', templateId);
      console.log('📝 Template params:', templateParams);

      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        templateId,
        templateParams,
        EMAILJS_CONFIG.publicKey
      );

      console.log('📬 EmailJS response:', result);

      if (result.status === 200) {
        console.log(`✅ ${planType} welcome email sent successfully to ${to}`);
        return true;
      } else {
        console.error('❌ EmailJS failed with status:', result.status);
        return false;
      }

    } catch (error) {
      console.error('❌ Error sending welcome email via EmailJS:', error);
      
      // Log more details about the error
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
      
      return false;
    }
  },

  // Test function to verify EmailJS is working
  sendTestEmail: async (to: string = '<EMAIL>'): Promise<boolean> => {
    try {
      console.log('🧪 Starting test email...');
      initializeEmailJS();

      if (!isInitialized) {
        console.error('❌ EmailJS not initialized for test');
        return false;
      }

      const templateParams = {
        to_email: to,
        to_name: 'Test User',
        user_name: 'Test User',
        plan_type: 'free',
        plan_name: 'Free',
        from_name: 'AutoML Pro Team',
        reply_to: '<EMAIL>',
        site_url: typeof window !== 'undefined' ? window.location.origin : 'https://automlpro.netlify.app',
        dashboard_url: typeof window !== 'undefined' ? `${window.location.origin}/dashboard` : 'https://automlpro.netlify.app/dashboard',
        pricing_url: typeof window !== 'undefined' ? `${window.location.origin}/pricing` : 'https://automlpro.netlify.app/pricing',
        chat_url: typeof window !== 'undefined' ? `${window.location.origin}/chat` : 'https://automlpro.netlify.app/chat',
      };

      console.log('🧪 Test email params:', templateParams);

      const result = await emailjs.send(
        EMAILJS_CONFIG.serviceId,
        EMAILJS_CONFIG.templateIds.free,
        templateParams,
        EMAILJS_CONFIG.publicKey
      );

      console.log('🧪 Test email result:', result);

      return result.status === 200;
    } catch (error) {
      console.error('❌ Test email failed:', error);
      return false;
    }
  }
};

// Export configuration for debugging
export const getEmailJSConfig = () => EMAILJS_CONFIG;

// Debug function to test EmailJS manually
export const debugEmailJS = async () => {
  console.log('🔍 EmailJS Debug Information:');
  console.log('Configuration:', EMAILJS_CONFIG);
  console.log('Initialized:', isInitialized);
  
  if (typeof window !== 'undefined') {
    console.log('Window object available');
    console.log('EmailJS object:', (window as any).emailjs);
  }
  
  // Try to send a test email
  console.log('🧪 Attempting test email...');
  const result = await emailJSService.sendTestEmail();
  console.log('🧪 Test result:', result);
  
  return result;
};