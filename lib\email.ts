import { supabase } from '@/lib/supabase';

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'free' | 'pro' | 'enterprise';
}

export const emailService = {
  sendWelcomeEmail: async ({ to, name, planType }: EmailRequest): Promise<boolean> => {
    try {
      const { data, error } = await supabase.functions.invoke('send-welcome-email', {
        body: {
          to,
          name,
          planType,
        },
      });

      if (error) {
        console.error('Error sending welcome email:', error);
        return false;
      }

      console.log('Welcome email sent successfully:', data);
      return true;
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      return false;
    }
  },

  // Send payment URL email for pro users
  sendPaymentEmail: async (to: string, name: string, paymentUrl: string): Promise<boolean> => {
    try {
      // This would be another edge function for payment emails
      const { data, error } = await supabase.functions.invoke('send-payment-email', {
        body: {
          to,
          name,
          paymentUrl,
        },
      });

      if (error) {
        console.error('Error sending payment email:', error);
        return false;
      }

      console.log('Payment email sent successfully:', data);
      return true;
    } catch (error) {
      console.error('Failed to send payment email:', error);
      return false;
    }
  },
};