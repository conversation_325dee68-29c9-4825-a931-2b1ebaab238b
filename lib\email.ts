import { emailJSService } from '@/lib/email-js';

export interface EmailRequest {
  to: string;
  name: string;
  planType: 'free' | 'pro' | 'enterprise';
}

export const emailService = {
  sendWelcomeEmail: async ({ to, name, planType }: EmailRequest): Promise<boolean> => {
    try {
      console.log(`📧 Sending ${planType} welcome email to ${to}...`);
      
      const success = await emailJSService.sendWelcomeEmail({ to, name, planType });
      
      if (success) {
        console.log(`✅ Welcome email sent successfully to ${to}`);
        return true;
      } else {
        console.error(`❌ Failed to send welcome email to ${to}`);
        return false;
      }
    } catch (error) {
      console.error('Email service error:', error);
      return false;
    }
  },

  // Test function for debugging
  sendTestEmail: async (to: string = 'rohan<PERSON><PERSON><EMAIL>'): Promise<boolean> => {
    try {
      console.log(`🧪 Sending test email to ${to}...`);
      
      const success = await emailJSService.sendTestEmail(to);
      
      if (success) {
        console.log(`✅ Test email sent successfully to ${to}`);
        return true;
      } else {
        console.error(`❌ Failed to send test email to ${to}`);
        return false;
      }
    } catch (error) {
      console.error('Test email service error:', error);
      return false;
    }
  }
};