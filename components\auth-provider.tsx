'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase, Profile } from '@/lib/supabase';
import { emailService } from '@/lib/email';
import { toast } from 'sonner';

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  session: Session | null;
  loading: boolean;
  signingOut: boolean;
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [signingOut, setSigningOut] = useState(false);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
        } else {
          setSession(session);
          setUser(session?.user ?? null);
          
          if (session?.user) {
            await fetchProfile(session.user.id);
          }
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.email);
        
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          await fetchProfile(session.user.id);
          
          // Handle new user signup (both email/password and OAuth)
          if (event === 'SIGNED_UP' || (event === 'SIGNED_IN' && !profile)) {
            console.log('🆕 New user detected, checking if profile exists...');
            
            // Small delay to ensure profile creation is complete
            setTimeout(async () => {
              await handleNewUserWelcome(session.user);
            }, 2000);
          }
        } else {
          setProfile(null);
        }
        
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const handleNewUserWelcome = async (user: User) => {
    try {
      console.log('🎉 Handling new user welcome for:', user.email);
      
      // Check if this is truly a new user by checking profile creation time
      const { data: profileData, error } = await supabase
        .from('profiles')
        .select('created_at')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error checking profile:', error);
        return;
      }

      // Check if profile was created recently (within last 30 seconds)
      const profileCreatedAt = new Date(profileData.created_at);
      const now = new Date();
      const timeDiff = now.getTime() - profileCreatedAt.getTime();
      const isNewProfile = timeDiff < 30000; // 30 seconds

      console.log('Profile created at:', profileCreatedAt);
      console.log('Time difference:', timeDiff, 'ms');
      console.log('Is new profile:', isNewProfile);

      if (isNewProfile && user.email) {
        console.log('📧 Sending welcome email to new user...');
        
        // Get user name from metadata or email
        const userName = user.user_metadata?.name || 
                        user.user_metadata?.full_name || 
                        user.email.split('@')[0];

        console.log('User name for email:', userName);

        try {
          const emailSent = await emailService.sendWelcomeEmail({
            to: user.email,
            name: userName,
            planType: 'free',
          });

          if (emailSent) {
            console.log('✅ Welcome email sent successfully!');
            toast.success('Welcome! Check your email for a welcome message.');
          } else {
            console.warn('⚠️ Welcome email failed to send');
            toast.success('Welcome to AutoML Pro!');
          }
        } catch (emailError) {
          console.error('❌ Welcome email error:', emailError);
          toast.success('Welcome to AutoML Pro!');
        }
      } else {
        console.log('👋 Existing user signed in, no welcome email needed');
      }
    } catch (error) {
      console.error('Error in handleNewUserWelcome:', error);
    }
  };

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching profile:', error);
        return;
      }

      setProfile(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const createProfile = async (user: User, name: string) => {
    try {
      console.log('👤 Creating profile for:', user.email);
      
      const { error } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          email: user.email!,
          name,
          avatar_url: user.user_metadata?.avatar_url,
        });

      if (error) {
        console.error('Error creating profile:', error);
        throw error;
      }

      console.log('✅ Profile created successfully');
      await fetchProfile(user.id);
    } catch (error) {
      console.error('Error creating profile:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, name: string) => {
    try {
      console.log('🚀 Starting email/password signup for:', email);
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) {
        console.error('Supabase signup error:', error);
        throw error;
      }

      console.log('✅ Supabase signup successful:', data.user?.email);

      if (data.user) {
        // Create profile
        await createProfile(data.user, name);
        
        // Send welcome email for email/password signup
        console.log('📧 Sending welcome email for email/password signup...');
        try {
          const emailSent = await emailService.sendWelcomeEmail({
            to: email,
            name,
            planType: 'free',
          });

          if (emailSent) {
            console.log('✅ Welcome email sent successfully!');
            toast.success('Account created successfully! Check your email for a welcome message.');
          } else {
            console.warn('⚠️ Welcome email failed to send');
            toast.success('Account created successfully! (Welcome email delivery failed)');
          }
        } catch (emailError) {
          console.error('❌ Welcome email error:', emailError);
          toast.success('Account created successfully! (Welcome email delivery failed)');
        }
      }
    } catch (error: any) {
      console.error('❌ Signup error:', error);
      toast.error(error.message || 'Failed to create account');
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      toast.success('Welcome back!');
    } catch (error: any) {
      console.error('Error signing in:', error);
      toast.error(error.message || 'Failed to sign in');
      throw error;
    }
  };

  const signInWithGoogle = async () => {
    try {
      console.log('🔗 Starting Google OAuth...');
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: typeof window !== 'undefined' ? `${window.location.origin}/auth/callback` : undefined,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) {
        console.error('Supabase OAuth error:', error);
        throw error;
      }

      console.log('✅ OAuth initiated successfully');
    } catch (error: any) {
      console.error('Error signing in with Google:', error);
      toast.error(error.message || 'Failed to sign in with Google');
      throw error;
    }
  };

  const signOut = async () => {
    if (signingOut) {
      console.log('⏳ Signout already in progress...');
      return;
    }

    try {
      setSigningOut(true);
      console.log('🚪 Starting signout process...');

      await supabase.auth.signOut();

      setUser(null);
      setProfile(null);
      setSession(null);

      console.log('✅ Signout successful');
      toast.success('Signed out successfully');
    } catch (error: any) {
      console.error('❌ Error signing out:', error);
      toast.error(error.message || 'Failed to sign out');
      throw error;
    } finally {
      setSigningOut(false);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) {
      throw new Error('No user logged in');
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      await fetchProfile(user.id);
      toast.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Error updating profile:', error);
      toast.error(error.message || 'Failed to update profile');
      throw error;
    }
  };

  const value = {
    user,
    profile,
    session,
    loading,
    signingOut,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}