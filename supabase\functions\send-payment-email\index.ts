import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PaymentEmailRequest {
  to: string
  name: string
  paymentUrl: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { to, name, paymentUrl }: PaymentEmailRequest = await req.json()

    const emailTemplate = {
      subject: '💳 AutoML Pro - Your Payment Link is Ready!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
          <div style="background: white; padding: 40px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #1f2937; margin: 0; font-size: 28px;">🧠 AutoML Pro</h1>
              <p style="color: #6b7280; margin: 10px 0 0 0;">AI-Powered Machine Learning Platform</p>
            </div>
            
            <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 30px;">
              <h2 style="margin: 0; font-size: 24px;">🎉 Your Payment Link is Ready!</h2>
              <p style="margin: 10px 0 0 0; opacity: 0.9;">Complete your Professional Plan subscription</p>
            </div>
            
            <h2 style="color: #1f2937; margin-bottom: 20px;">Hi ${name}! 🚀</h2>
            
            <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
              Great news! Our Professional Plan is now ready, and we're excited to activate your subscription with the early bird pricing you secured.
            </p>
            
            <div style="background: #fef3c7; border: 1px solid #f59e0b; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #92400e; margin: 0 0 10px 0;">💰 Your Locked-in Pricing</h3>
              <p style="color: #92400e; margin: 0; line-height: 1.6;">
                <strong>$15/month</strong> (Save $5 from regular price)
                <br>
                <small>This special early bird pricing is locked in for you!</small>
              </p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${paymentUrl}" 
                 style="background: #10b981; color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; font-size: 18px;">
                Complete Your Subscription
              </a>
            </div>
            
            <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #1f2937; margin: 0 0 15px 0;">🎯 What You Get:</h3>
              <ul style="color: #374151; margin: 0; padding-left: 20px;">
                <li><strong>50 ML models per month</strong></li>
                <li><strong>Large datasets (up to 10GB)</strong></li>
                <li><strong>Priority email support</strong></li>
                <li><strong>Fast processing speed</strong></li>
                <li><strong>Advanced analytics</strong></li>
                <li><strong>Private model hosting</strong></li>
                <li><strong>API access</strong></li>
                <li><strong>Custom preprocessing</strong></li>
              </ul>
            </div>
            
            <div style="background: #dbeafe; border: 1px solid #3b82f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #1e40af; margin: 0 0 10px 0;">🔒 Secure Payment</h3>
              <p style="color: #1e40af; margin: 0; line-height: 1.6;">
                Your payment is processed securely through our trusted payment partner. Your subscription will be activated immediately after payment.
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px; margin: 0;">
                Questions about your subscription? 
                <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a>
                <br>
                <a href="${Deno.env.get('SITE_URL') || 'https://automlpro.netlify.app'}/help" style="color: #3b82f6;">Visit our Help Center</a>
              </p>
            </div>
          </div>
        </div>
      `
    }
    
    console.log(`Sending payment email to ${to}`)
    console.log(`Payment URL: ${paymentUrl}`)
    
    // Here you would integrate with your email service
    // For now, we'll simulate sending the email
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Payment email sent successfully',
        emailSent: true 
      }),
      { 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )

  } catch (error) {
    console.error('Error sending payment email:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )
  }
})