'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Brain, Menu, X, Download, User, TestTube } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/theme-toggle';
import { useAuth } from '@/components/auth-provider';
import { useState } from 'react';
import { toast } from 'sonner';
import { debugEmailJS } from '@/lib/email-js';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const navItems = [
  { href: '/', label: 'Home' },
  { href: '/chat', label: 'Chat' },
  { href: '/pricing', label: 'Pricing' },
  { href: '/dashboard', label: 'Dashboard' },
];

export function Navigation() {
  const pathname = usePathname();
  const { user, profile, signOut } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const isHomePage = pathname === '/';

  const handleExportProject = () => {
    // Create a comprehensive project export
    const projectData = {
      user: user?.email || 'Unknown',
      timestamp: new Date().toISOString(),
      project: {
        name: 'AutoML Pro Project',
        description: 'Exported project from AutoML Pro platform',
        settings: {
          autoML: true,
          preprocessing: 'automatic',
          modelSelection: 'best_performance'
        }
      }
    };

    const content = `# AutoML Pro Project Export

## Project Information
- **User:** ${user?.email || 'Unknown'}
- **Export Date:** ${new Date().toLocaleDateString()}
- **Platform:** AutoML Pro

## Project Configuration
\`\`\`json
${JSON.stringify(projectData, null, 2)}
\`\`\`

## Getting Started
1. Upload your dataset to the platform
2. Configure your ML task using natural language
3. Monitor training progress in real-time
4. Download your trained model

## Files Included
- README.md (this file)
- config.json (project configuration)
- requirements.txt (dependencies)

## Support
Visit our documentation or contact support for assistance.

---
Generated by AutoML Pro - AI-Powered Machine Learning Platform
`;

    // Create and download the file
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `automl-project-export-${Date.now()}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Project exported successfully!');
  };

  const handleTestEmail = async () => {
    try {
      toast.info('Testing EmailJS configuration...');
      const result = await debugEmailJS();
      
      if (result) {
        toast.success('Test email sent successfully! Check <EMAIL>');
      } else {
        toast.error('Test email failed. Check console for details.');
      }
    } catch (error) {
      console.error('Test email error:', error);
      toast.error('Test email failed. Check console for details.');
    }
  };

  return (
    <motion.nav
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className={`fixed top-0 left-0 right-0 z-50 ${
        isHomePage ? 'glass' : 'bg-background/80 backdrop-blur-md'
      } border-b border-border/50`}
    >
      <div className="w-full px-6">
        <div className="flex items-center justify-between h-20 max-w-7xl mx-auto">
          {/* Logo - Fixed Left Position */}
          <div className="flex items-center w-72">
            <Link href="/" className="flex items-center space-x-2 group">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center space-x-3"
              >
                <Brain className="w-10 h-10" />
                <span className="text-2xl font-bold">AutoML Pro</span>
              </motion.div>
            </Link>
          </div>

          {/* Desktop Navigation - Perfectly Centered */}
          <div className="hidden md:flex items-center justify-center flex-1">
            <div className="flex items-center space-x-12">
              {navItems.map((item) => {
                // Only show Dashboard if user is logged in
                if (item.href === '/dashboard' && !user) return null;
                
                return (
                  <Link key={item.href} href={item.href}>
                    <motion.span
                      whileHover={{ scale: 1.05 }}
                      className={`text-base font-medium transition-colors ${
                        pathname === item.href
                          ? 'text-foreground'
                          : 'text-muted-foreground hover:text-foreground'
                      }`}
                    >
                      {item.label}
                    </motion.span>
                  </Link>
                );
              })}
            </div>
          </div>

          {/* Right Side Actions - Fixed Right Position */}
          <div className="hidden md:flex items-center justify-end space-x-4 w-72">
            <ThemeToggle />
            
            {user ? (
              <div className="flex items-center space-x-3">
                {/* Test Email Button (for debugging) */}
                <Button
                  variant="ghost"
                  size="default"
                  onClick={handleTestEmail}
                  className="flex items-center space-x-2"
                >
                  <TestTube className="w-4 h-4" />
                  <span>Test Email</span>
                </Button>
                
                {/* Export Project Button */}
                <Button
                  variant="ghost"
                  size="default"
                  onClick={handleExportProject}
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Export Project</span>
                </Button>
                
                {/* Profile Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="default" className="flex items-center space-x-2">
                      <User className="w-4 h-4" />
                      <span>Profile</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>My Account</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <div className="px-2 py-1.5 text-sm">
                      <div className="font-medium">{profile?.name || 'User'}</div>
                      <div className="text-muted-foreground text-xs">{user.email}</div>
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard" className="cursor-pointer">
                        Dashboard
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/settings" className="cursor-pointer">
                        Settings
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={signOut} className="cursor-pointer text-red-600">
                      Sign Out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link href="/signin">
                  <Button variant="ghost" size="default">
                    Sign In
                  </Button>
                </Link>
                <Link href="/signup">
                  <Button size="default">
                    Sign Up
                  </Button>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center space-x-3">
            <ThemeToggle />
            <Button
              variant="ghost"
              size="default"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{
            opacity: mobileMenuOpen ? 1 : 0,
            height: mobileMenuOpen ? 'auto' : 0,
          }}
          transition={{ duration: 0.3 }}
          className="md:hidden overflow-hidden"
        >
          <div className="py-6 space-y-6">
            {navItems.map((item) => {
              // Only show Dashboard if user is logged in
              if (item.href === '/dashboard' && !user) return null;
              
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => setMobileMenuOpen(false)}
                  className={`block text-base font-medium ${
                    pathname === item.href
                      ? 'text-foreground'
                      : 'text-muted-foreground'
                  }`}
                >
                  {item.label}
                </Link>
              );
            })}
            
            <div className="pt-6 space-y-4">
              {user ? (
                <>
                  <div className="text-base text-muted-foreground mb-4">
                    <div className="font-medium">{profile?.name || 'User'}</div>
                    <div className="text-sm">{user.email}</div>
                  </div>
                  <Button
                    variant="ghost"
                    size="default"
                    onClick={() => {
                      handleTestEmail();
                      setMobileMenuOpen(false);
                    }}
                    className="w-full justify-start"
                  >
                    <TestTube className="w-4 h-4 mr-2" />
                    Test Email
                  </Button>
                  <Button
                    variant="ghost"
                    size="default"
                    onClick={() => {
                      handleExportProject();
                      setMobileMenuOpen(false);
                    }}
                    className="w-full justify-start"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export Project
                  </Button>
                  <Button 
                    variant="outline" 
                    size="default" 
                    className="w-full" 
                    onClick={() => {
                      signOut();
                      setMobileMenuOpen(false);
                    }}
                  >
                    Sign Out
                  </Button>
                </>
              ) : (
                <>
                  <Link href="/signin" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="ghost" size="default" className="w-full">
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/signup" onClick={() => setMobileMenuOpen(false)}>
                    <Button size="default" className="w-full">
                      Sign Up
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </motion.nav>
  );
}