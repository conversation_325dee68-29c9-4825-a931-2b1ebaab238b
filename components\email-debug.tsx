'use client';

import { useState } from 'react';
import { emailService } from '@/lib/email';
import { testEmailJS, getEmailJSConfig, getInitializationStatus } from '@/lib/email-js';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';

export function EmailDebug() {
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testing, setTesting] = useState(false);

  const handleTestDirect = async () => {
    setTesting(true);
    try {
      console.log('🧪 Testing EmailJS directly...');
      const result = await testEmailJS(testEmail);
      
      if (result) {
        toast.success('✅ Direct EmailJS test successful!');
      } else {
        toast.error('❌ Direct EmailJS test failed. Check console.');
      }
    } catch (error) {
      console.error('Direct test error:', error);
      toast.error('❌ Direct test failed with error.');
    } finally {
      setTesting(false);
    }
  };

  const handleTestService = async () => {
    setTesting(true);
    try {
      console.log('🧪 Testing email service...');
      const result = await emailService.sendWelcomeEmail({
        to: testEmail,
        name: 'Test User',
        planType: 'free'
      });
      
      if (result) {
        toast.success('✅ Email service test successful!');
      } else {
        toast.error('❌ Email service test failed. Check console.');
      }
    } catch (error) {
      console.error('Service test error:', error);
      toast.error('❌ Service test failed with error.');
    } finally {
      setTesting(false);
    }
  };

  const showDebugInfo = () => {
    const config = getEmailJSConfig();
    const isInit = getInitializationStatus();
    
    console.log('📧 EmailJS Debug Info:');
    console.log('- Configuration:', config);
    console.log('- Initialized:', isInit);
    console.log('- Window object:', typeof window);
    console.log('- EmailJS available:', typeof window !== 'undefined' && 'emailjs' in window);
    
    toast.info('Debug info logged to console');
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 bg-background border rounded-lg p-4 max-w-sm space-y-3 z-50">
      <h3 className="font-bold text-sm">📧 Email Debug (Dev Only)</h3>
      
      <Input
        type="email"
        placeholder="Test email address"
        value={testEmail}
        onChange={(e) => setTestEmail(e.target.value)}
        className="text-xs"
      />
      
      <div className="space-y-2">
        <Button 
          onClick={handleTestDirect}
          disabled={testing}
          size="sm"
          className="w-full text-xs"
        >
          {testing ? 'Testing...' : 'Test EmailJS Direct'}
        </Button>
        
        <Button 
          onClick={handleTestService}
          disabled={testing}
          size="sm"
          variant="outline"
          className="w-full text-xs"
        >
          {testing ? 'Testing...' : 'Test Email Service'}
        </Button>
        
        <Button 
          onClick={showDebugInfo}
          size="sm"
          variant="ghost"
          className="w-full text-xs"
        >
          Show Debug Info
        </Button>
      </div>
    </div>
  );
}
