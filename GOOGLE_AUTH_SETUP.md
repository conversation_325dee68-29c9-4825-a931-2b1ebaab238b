# Google Authentication Setup Guide

## Issues Found and Fixed

### 1. **Environment Variable Issues**
- ❌ **Problem**: Using `VITE_` prefix instead of `NEXT_PUBLIC_` for Next.js
- ❌ **Problem**: Hardcoded Firebase config instead of environment variables
- ✅ **Fixed**: Updated to use proper Next.js environment variables

### 2. **Authentication Flow Issues**
- ❌ **Problem**: Trying to use Firebase + Supabase hybrid approach
- ❌ **Problem**: Using unsupported `signInWithIdToken` method
- ✅ **Fixed**: Switched to Supabase native Google OAuth

### 3. **Missing Configuration**
- ❌ **Problem**: No Google OAuth setup in Supabase dashboard
- ❌ **Problem**: Missing redirect URLs configuration

## Setup Instructions

### Step 1: Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
4. Create OAuth 2.0 credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Application type: "Web application"
   - Add authorized redirect URIs:
     ```
     https://your-project-ref.supabase.co/auth/v1/callback
     http://localhost:3000/auth/callback (for development)
     ```
5. Copy the Client ID and Client Secret

### Step 2: Supabase Dashboard Setup

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Navigate to Authentication > Providers
3. Enable Google provider
4. Add your Google OAuth credentials:
   - Client ID: (from Google Cloud Console)
   - Client Secret: (from Google Cloud Console)
5. Set redirect URL: `https://your-domain.com/auth/callback`

### Step 3: Environment Variables

Update your `.env` file:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Google OAuth Configuration
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
```

### Step 4: Test the Authentication

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to `/signin` or `/signup`
3. Click "Continue with Google"
4. You should be redirected to Google's OAuth consent screen
5. After authorization, you'll be redirected back to your app

## Troubleshooting

### Common Issues:

1. **"redirect_uri_mismatch" error**
   - Check that your redirect URIs in Google Cloud Console match exactly
   - Make sure to include both production and development URLs

2. **"Invalid client" error**
   - Verify your Google Client ID is correct in Supabase dashboard
   - Ensure the Google+ API is enabled

3. **User not created in database**
   - Check that the database trigger `handle_new_user()` is working
   - Verify RLS policies allow user creation

4. **Redirect not working**
   - Check that your redirect URL in Supabase matches your domain
   - Ensure the callback route exists in your app

### Debug Steps:

1. Check browser console for errors
2. Check Supabase logs in dashboard
3. Verify environment variables are loaded correctly
4. Test with a simple OAuth flow first

## Next Steps

After Google authentication is working:

1. Test the complete user flow
2. Add error handling for edge cases
3. Implement proper loading states
4. Add logout functionality
5. Test profile creation and updates

## Security Notes

- Never commit your Google Client Secret to version control
- Use environment variables for all sensitive data
- Implement proper CSRF protection
- Validate user data on the server side
