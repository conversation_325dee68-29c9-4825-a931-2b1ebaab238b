import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "AIzaSyA2_Dx7aKmiHuTIirK0PMkHFJ7Wkf7gR5U",
  authDomain: "vault-link.firebaseapp.com",
  projectId: "vault-link",
  storageBucket: "vault-link.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:76be29d810d6a43ad8aa14",
  measurementId: "G-29WM8DFJLP"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth
export const auth = getAuth(app);

// Initialize Google Auth Provider
export const googleProvider = new GoogleAuthProvider();
googleProvider.setCustomParameters({
  prompt: 'select_account',
});

export default app;