# AutoML Pro - AI-Powered Machine Learning Platform

A complete full-stack SaaS platform that allows users to create machine learning models using natural language prompts. Built with Next.js, FastAPI, PostgreSQL, and Celery for background processing.

## 🌟 Features

### Frontend
- **Modern UI/UX**: Beautiful, responsive design with Framer Motion animations
- **Dark/Light Mode**: Toggle between themes with smooth transitions
- **Authentication**: Secure login/signup with JWT tokens and Google OAuth
- **Real-time Updates**: Live job status tracking and progress monitoring
- **Dashboard**: Comprehensive project management and job history
- **Results Visualization**: Detailed model performance metrics and insights

### Backend
- **FastAPI**: High-performance Python web framework with automatic API documentation
- **PostgreSQL**: Robust database for storing users, jobs, and results
- **Celery + Redis**: Distributed task queue for ML processing
- **JWT Security**: Secure authentication and authorization
- **RESTful API**: Clean, well-documented API endpoints

### ML Pipeline
- **Natural Language Processing**: Convert user prompts into ML tasks
- **Automated Training**: Background processing with real-time updates
- **Model Results**: Comprehensive metrics, feature importance, and sample predictions
- **Extensible Architecture**: Easy to integrate with real ML frameworks

## 🏗️ Architecture

```
├── Frontend (Next.js)
│   ├── Authentication & User Management
│   ├── Dashboard & Job Management
│   ├── Real-time Status Updates
│   └── Results Visualization
│
├── Backend (FastAPI)
│   ├── RESTful API Endpoints
│   ├── JWT Authentication
│   ├── Database Models & Services
│   └── Task Queue Integration
│
├── Background Processing (Celery)
│   ├── ML Task Processing
│   ├── Progress Tracking
│   └── Result Generation
│
└── Infrastructure
    ├── PostgreSQL Database
    ├── Redis Cache/Queue
    └── Docker Compose Setup
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Python 3.11+
- Docker and Docker Compose
- PostgreSQL (if running locally)
- Redis (if running locally)

### 1. Clone the Repository
```bash
git clone <repository-url>
cd automl-saas-platform
```

### 2. Environment Setup
```bash
# Copy environment variables
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 3. Using Docker Compose (Recommended)
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- Celery Flower (monitoring): http://localhost:5555

### 4. Manual Setup (Development)

#### Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Start PostgreSQL and Redis (if not using Docker)
# Then run migrations and start the server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Start Celery Worker
```bash
cd backend
celery -A app.workers.celery_app worker --loglevel=info
```

#### Frontend Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/signup` - Create new user account
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `GET /auth/google` - Google OAuth login

### Job Management Endpoints
- `POST /api/prompt` - Submit new ML task
- `GET /api/jobs` - Get user's job history
- `GET /api/status/{job_id}` - Get job status and progress
- `GET /api/result/{job_id}` - Get job results and metrics

### System Endpoints
- `GET /health` - Health check
- `GET /api/celery/status` - Celery worker status

Visit http://localhost:8000/docs for interactive API documentation.

## 🎨 UI Components

The frontend includes a comprehensive set of reusable components:

- **Authentication**: Login/signup forms with validation
- **Dashboard**: Job management and statistics
- **Job Status**: Real-time progress tracking
- **Results**: Model performance visualization
- **Navigation**: Responsive header with dark mode toggle
- **Animations**: Smooth transitions and micro-interactions

## 🔧 Configuration

### Environment Variables

#### Frontend (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:8000
```

#### Backend (.env)
```
DATABASE_URL=postgresql://user:pass@localhost:5432/automl_db
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

### Database Setup
```bash
# Using Docker Compose (recommended)
docker-compose up postgres redis

# Or install locally and create database
createdb automl_db
```

## 🧪 Testing

### Frontend Tests
```bash
npm run test
npm run test:e2e
```

### Backend Tests
```bash
cd backend
pytest
```

## 📦 Deployment

### Production Build
```bash
# Frontend
npm run build
npm start

# Backend
pip install -r requirements.txt
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### Docker Production
```bash
# Build and run production containers
docker-compose -f docker-compose.prod.yml up -d
```

### Environment Setup for Production
- Use strong SECRET_KEY
- Set up proper database credentials
- Configure Google OAuth with production URLs
- Set up SSL certificates
- Configure environment-specific variables

## 🔮 Future ML Integration

The platform is designed to easily integrate with real ML frameworks:

1. **Replace Mock ML Service**: Update `backend/app/ml_service/train.py` with actual ML training code
2. **Add Data Processing**: Implement data upload, validation, and preprocessing
3. **Model Registry**: Add model versioning and management
4. **Deployment Pipeline**: Integrate with cloud ML services (AWS SageMaker, Google Cloud ML, etc.)
5. **Monitoring**: Add model performance monitoring and drift detection

### Suggested ML Stack Integration
- **Scikit-learn**: Traditional ML algorithms
- **TensorFlow/PyTorch**: Deep learning models
- **XGBoost/LightGBM**: Gradient boosting
- **MLflow**: Model tracking and registry
- **Kubeflow**: Kubernetes-native ML workflows

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- Create an issue for bug reports
- Join our Discord community for discussions
- Check the documentation for common questions

## 🎯 Roadmap

- [ ] Real ML model training integration
- [ ] Data upload and management
- [ ] Model deployment and serving
- [ ] Advanced visualization dashboard
- [ ] Team collaboration features
- [ ] API rate limiting and usage tracking
- [ ] Advanced authentication (SSO, RBAC)
- [ ] Model marketplace
- [ ] Mobile app
- [ ] Advanced analytics and reporting

---

Built with ❤️ for the AI community. Start building intelligent applications today!